<template>
	<PageLayout iconColor="#000" :isBackgroundColor="false" :isShowBack="false" :navTitle="navTitle"
		:bgMaskStyle="bgMaskStyleComputed">

	</PageLayout>
</template>

<script>
import PageLayout from "@/components/PageLayout/index.vue";

export default {
	components: {
		PageLayout
	},
	data() {
		return {
			navTitle: "购物车",
		}
	},
	computed: {
		bgMaskStyleComputed() {
			return {
				"--bg-mask-background": `linear-gradient(180deg, #F8D7D6 1.04%, #F5F6FA 7.69%);`
			}
		}
	}
}
</script>

<style scoped></style>